import React, { useState } from 'react';
import Navbar from '../components/Navbar';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { stripeService } from '../services/api/stripe.service';
import { showErrorToast } from '../utils/showErrorToast';
import { showSuccessToast } from '../utils/showSuccessToast';
import useUserDataStore from '../store/useUserDataStore';
import { FaCrown, FaCoins, FaCheck, FaRegClock, FaTimesCircle } from 'react-icons/fa';
import { PremiumPlan, GoldPackageType } from '../types/premium';

export default function ShopPage() {
  useAuthGuard();
  const { userData, fetchUserData } = useUserDataStore();
  const [loadingSubscription, setLoadingSubscription] = useState(false);
  const [loadingGold, setLoadingGold] = useState(false);
  const [selectedGoldPackage, setSelectedGoldPackage] = useState(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancellingSubscription, setCancellingSubscription] = useState(false);
  const [subscriptionInfo, setSubscriptionInfo] = useState({ isActive: false, endsAt: null, status: null, premiumExpiresAt: null });

  const isPremium = userData?.isPremium || false;

  // Fetch subscription details when component mounts
  React.useEffect(() => {
    if (isPremium) {
      const getSubscriptionInfo = async () => {
        try {
          const info = await stripeService.verifySubscription();

          // Get subscription status and premium expiration date from userData if available
          const status = userData?.subscriptionStatus || null;
          const premiumExpiresAt = userData?.premiumExpiresAt || null;

          setSubscriptionInfo({
            ...info,
            status: status,
            premiumExpiresAt: premiumExpiresAt
          });
        } catch (error) {
          console.error('Failed to fetch subscription info:', error);
        }
      };

      getSubscriptionInfo();
    }
  }, [isPremium, userData?.subscriptionStatus, userData?.premiumExpiresAt]);

  const subscriptionPlans = [
    {
      id: PremiumPlan.MONTHLY,
      name: 'Monthly Premium',
      price: '€2.00',
      period: 'per month',
      features: [
        '50% faster training times',
        'Auto-mode in wars',
        'Premium badge',
        'Priority customer support'
      ],
      popular: false,
      savings: null
    },
    {
      id: PremiumPlan.SEMIANNUAL,
      name: '6-Month Premium',
      price: '€9.00',
      period: 'per 6 months',
      features: [
        '50% faster training times',
        'Auto-mode in wars',
        'Premium badge',
        'Priority customer support',
        '25% discount compared to monthly'
      ],
      popular: true,
      savings: '€3.00'
    },
    {
      id: PremiumPlan.YEARLY,
      name: 'Yearly Premium',
      price: '€12.00',
      period: 'per year',
      features: [
        '50% faster training times',
        'Auto-mode in wars',
        'Premium badge',
        'Priority customer support',
        '50% discount compared to monthly'
      ],
      popular: false,
      savings: '€12.00'
    }
  ];

  const goldPackages = [
    {
      id: GoldPackageType.SMALL,
      name: 'Starter Pack',
      amount: 1000,
      price: '€4.99',
      bonus: 100,
      popular: false
    },
    {
      id: GoldPackageType.MEDIUM,
      name: 'Advanced Pack',
      amount: 3000,
      price: '€9.99',
      bonus: 500,
      popular: true
    },
    {
      id: GoldPackageType.LARGE,
      name: 'Elite Pack',
      amount: 10000,
      price: '€24.99',
      bonus: 2000,
      popular: false
    },
    {
      id: GoldPackageType.EXTRA_LARGE,
      name: 'Ultimate Pack',
      amount: 25000,
      price: '€49.99',
      bonus: 5000,
      popular: false
    }
  ];

  const handleSubscribe = async (planId) => {
    setLoadingSubscription(true);
    try {
      // Create success and cancel URLs with query parameters
      const successUrl = `/payment/success?type=subscription&plan=${planId}`;
      const cancelUrl = `/payment/cancel?type=subscription&plan=${planId}`;

      const { url } = await stripeService.createSubscriptionSession(
        planId,
        successUrl,
        cancelUrl
      );
      // Redirect to the Stripe checkout page
      window.location.href = url;
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoadingSubscription(false);
    }
  };

  const handleBuyGold = async (packageId) => {
    setLoadingGold(true);
    setSelectedGoldPackage(packageId);

    try {
      const goldPackage = goldPackages.find(pkg => pkg.id === packageId);
      const amount = goldPackage.amount;

      // Create success and cancel URLs with query parameters
      const successUrl = `/payment/success?type=gold&package=${packageId}&amount=${amount}`;
      const cancelUrl = `/payment/cancel?type=gold&package=${packageId}`;

      // Create a checkout session for the gold purchase
      const { url } = await stripeService.createGoldCheckoutSession(
        packageId,
        amount + (goldPackage.bonus || 0), // Pass total gold amount (base + bonus)
        successUrl,
        cancelUrl,
        'eur' // Use EUR as the currency
      );

      // Redirect to the Stripe checkout page
      window.location.href = url;
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoadingGold(false);
      setSelectedGoldPackage(null);
    }
  };

  const handleCancelSubscription = async () => {
    setCancellingSubscription(true);
    try {
      const response = await stripeService.cancelSubscription();
      if (response.success) {
        showSuccessToast(response.message || 'Your subscription has been cancelled successfully.');
        // Refresh user data to update premium status
        await fetchUserData(true);

        // Update subscription info with cancel_at_period_end status
        setSubscriptionInfo(prevInfo => ({
          ...prevInfo,
          status: 'cancel_at_period_end'
        }));

        // Close the dialog
        setShowCancelDialog(false);
      } else {        
        showErrorToast(response || 'Failed to cancel subscription.');
      }
    } catch (error) {      
      showErrorToast(error);
    } finally {
      setCancellingSubscription(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neonBlue mb-4">Shop</h1>
          {/* <p className="text-gray-400 max-w-2xl mx-auto">
            Currently not available!
          </p> */}
        </div>

        {/* Subscription Status */}
        {isPremium && (
          <div className="bg-gray-800 rounded-lg p-8 mb-12 max-w-3xl mx-auto border border-yellow-500 shadow-lg">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center justify-center">
                <FaCrown className="text-yellow-400 text-2xl mr-2" />
                Your Premium Subscription
              </h2>
            </div>

            <div className="flex flex-wrap justify-center gap-4 mb-6">
              <div className={`px-5 py-2 rounded-md font-medium ${
                subscriptionInfo.status === 'cancel_at_period_end'
                  ? 'bg-yellow-900 text-yellow-300'
                  : 'bg-green-900 text-green-400'
              }`}>
                Status: {subscriptionInfo.status === 'cancel_at_period_end' ? 'Canceling' : 'Active'}
              </div>

              {subscriptionInfo.premiumExpiresAt && (
                <div className="bg-gray-700 px-5 py-2 rounded-md text-gray-300">
                  Premium expires: {new Date(subscriptionInfo.premiumExpiresAt).toLocaleDateString()} at {new Date(subscriptionInfo.premiumExpiresAt).toLocaleTimeString()}
                </div>
              )}
            </div>

            {subscriptionInfo.status === 'cancel_at_period_end' && (
              <div className="bg-yellow-900/30 border border-yellow-700 text-yellow-300 p-4 rounded-md mb-6 max-w-xl mx-auto">
                <p className="flex items-center justify-center">
                  <FaTimesCircle className="mr-2 flex-shrink-0" />
                  Your subscription has been canceled but will remain active until {subscriptionInfo.premiumExpiresAt ? new Date(subscriptionInfo.premiumExpiresAt).toLocaleDateString() : 'the end of the current billing period'}.
                </p>
              </div>
            )}

            <div className="bg-gray-700/50 p-6 rounded-md mb-6">
              <h3 className="text-white font-semibold mb-4 text-center">Active Premium Benefits:</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <FaCheck className="text-green-400 mt-1 mr-3 flex-shrink-0" />
                  <span className="text-gray-300">50% faster training times</span>
                </li>
                <li className="flex items-start">
                  <FaCheck className="text-green-400 mt-1 mr-3 flex-shrink-0" />
                  <span className="text-gray-300">Auto-mode in wars</span>
                </li>
                <li className="flex items-start">
                  <FaCheck className="text-green-400 mt-1 mr-3 flex-shrink-0" />
                  <span className="text-gray-300">Premium badge</span>
                </li>
                <li className="flex items-start">
                  <FaCheck className="text-green-400 mt-1 mr-3 flex-shrink-0" />
                  <span className="text-gray-300">Priority customer support</span>
                </li>
              </ul>
            </div>

            <p className="text-gray-400 mb-6 text-center">
              You can cancel your subscription at any time. Your premium benefits will remain active until the end of your current billing period.
            </p>

            {subscriptionInfo.status !== 'cancel_at_period_end' && (
              <div className="flex justify-center">
                <button
                  onClick={() => setShowCancelDialog(true)}
                  className="px-6 py-3 rounded-md font-medium bg-gray-900 hover:bg-gray-800 text-white border border-red-600 hover:border-red-500 transition-all"
                >
                  Cancel Subscription
                </button>
              </div>
            )}
          </div>
        )}

        {/* Premium Subscription Section */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white inline-flex items-center">
              <FaCrown className="text-yellow-400 mr-2" />
              Premium Subscription
            </h2>
            <p className="text-gray-400 mt-2">
              Unlock exclusive features and gain competitive advantages
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {subscriptionPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-gray-800 rounded-lg overflow-hidden shadow-lg border ${
                  plan.popular ? 'border-neonBlue' : 'border-gray-700'
                }`}
              >
                {plan.popular && (
                  <div className="bg-neonBlue text-white text-center py-1 font-medium">
                    MOST POPULAR
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-white">{plan.price}</span>
                    <span className="text-gray-400 ml-1">{plan.period}</span>
                    {plan.savings && (
                      <div className="mt-1 text-green-400 text-sm">
                        Save {plan.savings} compared to monthly
                      </div>
                    )}
                  </div>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <FaCheck className="text-green-400 mt-1 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={loadingSubscription || isPremium}
                    className={`w-full py-3 rounded font-medium transition-all transform hover:scale-105 ${
                      isPremium
                        ? 'bg-gray-600 cursor-not-allowed'
                        : plan.popular
                        ? 'bg-neonBlue hover:bg-blue-700 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-white'
                    }`}
                  >
                    {isPremium
                      ? 'Already Subscribed'
                      : loadingSubscription
                      ? 'Processing...'
                      : 'Subscribe Now'}
                  </button>
                  {isPremium && (
                    <p className="text-green-400 text-sm text-center mt-2">
                      You already have an active premium subscription
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cancellation Confirmation Dialog */}
        {showCancelDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 p-6 rounded-lg max-w-md w-full">
              <div className="flex justify-center mb-4">
                <FaTimesCircle className="text-red-500 text-4xl" />
              </div>
              <h2 className="text-xl font-bold text-white mb-4 text-center">Cancel Premium Subscription?</h2>
              <p className="text-gray-300 mb-6 text-center">
                Are you sure you want to cancel your premium subscription? You will lose access to all premium features at the end of your current billing period.
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowCancelDialog(false)}
                  className="flex-1 py-2 rounded font-medium bg-gray-700 hover:bg-gray-600 text-white"
                  disabled={cancellingSubscription}
                >
                  Keep Subscription
                </button>
                <button
                  onClick={handleCancelSubscription}
                  className="flex-1 py-2 rounded font-medium bg-red-600 hover:bg-red-700 text-white"
                  disabled={cancellingSubscription}
                >
                  {cancellingSubscription ? 'Cancelling...' : 'Yes, Cancel'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Gold Packages Section */}
        <div>
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white inline-flex items-center">
              <FaCoins className="text-yellow-400 mr-2" />
              Gold Packages
            </h2>
            <p className="text-gray-400 mt-2">
              Purchase gold to accelerate your progress and unlock special features
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {goldPackages.map((pkg) => (
              <div
                key={pkg.id}
                className={`bg-gray-800 rounded-lg overflow-hidden shadow-lg border ${
                  pkg.popular ? 'border-yellow-500' : 'border-gray-700'
                }`}
              >
                {pkg.popular && (
                  <div className="bg-yellow-500 text-white text-center py-1 font-medium">
                    BEST VALUE
                  </div>
                )}
                <div className="p-5">
                  <h3 className="text-lg font-bold text-white mb-2">{pkg.name}</h3>
                  <div className="flex items-center justify-center mb-4">
                    <span className="text-yellow-400 text-2xl mr-1"><FaCoins className="text-yellow-400 mr-2" /></span>
                    <span className="text-2xl font-bold text-white">{pkg.amount.toLocaleString()}</span>
                    {pkg.bonus && (
                      <span className="ml-2 bg-green-900 text-green-400 text-xs px-2 py-1 rounded">
                        +{pkg.bonus} BONUS
                      </span>
                    )}
                  </div>
                  <div className="text-center mb-5">
                    <span className="text-xl font-bold text-white">{pkg.price}</span>
                  </div>
                  <button
                    onClick={() => handleBuyGold(pkg.id)}
                    disabled={loadingGold}
                    className={`w-full py-2 rounded font-medium transition-all transform hover:scale-105 ${
                      pkg.popular
                        ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-white'
                    }`}
                  >
                    {loadingGold && selectedGoldPackage === pkg.id
                      ? 'Processing...'
                      : 'Buy Now'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits Section */}
        <div className="mt-16 bg-gray-800 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">Premium Benefits</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-700 p-5 rounded-lg">
              <div className="flex items-center mb-4">
                <FaRegClock className="text-neonBlue text-2xl mr-3" />
                <h3 className="text-lg font-semibold text-white">Faster Training</h3>
              </div>
              <p className="text-gray-300">
                Premium members enjoy 50% faster training times, allowing you to develop your character more quickly.
              </p>
            </div>
            <div className="bg-gray-700 p-5 rounded-lg">
              <div className="flex items-center mb-4">
                <FaCrown className="text-yellow-400 text-2xl mr-3" />
                <h3 className="text-lg font-semibold text-white">Auto-Mode in Wars</h3>
              </div>
              <p className="text-gray-300">
                Set up automatic participation in war/work, ensuring you never miss a battle/work even when you're offline.
              </p>
            </div>
            <div className="bg-gray-700 p-5 rounded-lg">
              <div className="flex items-center mb-4">
                <FaCoins className="text-yellow-400 text-2xl mr-3" />
                <h3 className="text-lg font-semibold text-white">Gold Benefits</h3>
              </div>
              <p className="text-gray-300">
                Use gold to accelerate training, purchase special items, and gain advantages in various game aspects.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
